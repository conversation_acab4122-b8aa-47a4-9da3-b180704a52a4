CREATE TABLE member_purchased_products (
    customer_code VARCHAR(12) NOT NULL,
    order_no VARCHAR(16,0),
    order_detail_no NUMERIC(16,0) NOT NULL,
    commodity_code VARCHAR(16,0) NOT NULL,
    order_date TIMESTAMP NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (order_no, order_detail_no)
);

CREATE INDEX idx_member_purchased_products ON member_purchased_products (customer_code, order_no, commodity_code, order_date);

COMMENT ON TABLE member_purchased_products IS '会員別購入商品';
COMMENT ON COLUMN member_purchased_products.customer_code IS '顧客番号';
COMMENT ON COLUMN member_purchased_products.order_no IS '受注番号';
COMMENT ON COLUMN member_purchased_products.order_detail_no IS '受注明細番号';
COMMENT ON COLUMN member_purchased_products.commodity_code IS '商品コード';
COMMENT ON COLUMN member_purchased_products.order_date IS '注文日';
COMMENT ON COLUMN member_purchased_products.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN member_purchased_products.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN member_purchased_products.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN member_purchased_products.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN member_purchased_products.d_version IS 'デ連バージョン';


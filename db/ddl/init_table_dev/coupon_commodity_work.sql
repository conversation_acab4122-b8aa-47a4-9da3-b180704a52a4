CREATE TABLE coupon_commodity_work (
    coupon_management_code VARCHAR(16) NOT NULL,
    shop_code VARCHAR(16) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    PRIMARY KEY (coupon_management_code, shop_code, commodity_code)
);
COMMENT ON TABLE coupon_commodity_work IS 'クーポン適用商品ワーク';
COMMENT ON COLUMN coupon_commodity_work.coupon_management_code IS 'クーポン管理コード';
COMMENT ON COLUMN coupon_commodity_work.shop_code IS 'ショップコード';
COMMENT ON COLUMN coupon_commodity_work.commodity_code IS '商品コード';
COMMENT ON COLUMN coupon_commodity_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN coupon_commodity_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN coupon_commodity_work.created_datetime IS '作成日時';
COMMENT ON COLUMN coupon_commodity_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN coupon_commodity_work.updated_datetime IS '更新日時';


#!/usr/bin/env python
# -*- coding: utf-8 -*-
# スクリプト先頭
from source.glue_deps_handler import setup_glue_dependencies

# 依存関係のセットアップを実行
setup_glue_dependencies()

import json
import os
import boto3
import paramiko
import io
import re
from ftplib import FTP
from office365.sharepoint.client_context import ClientContext
from office365.runtime.auth.user_credential import UserCredential
from io import BytesIO
from io import String<PERSON>
from typing import Dict, Any
from source.common import initialize_env, get_job_params, retry_function, ExternalConnection as ExCon, str_to_bool, get_callback_token, send_fail_callback_to_step_functions
from source.glue_logger import GlueLogger
from source.common_exception import FileNotFoundException


class GlueJobGetFile:
    """ファイル取得ジョブ"""

    # 定数定義
    PROTOCOL_TYPE_HTTP = "HTTP"
    PROTOCOL_TYPE_HTTPS = "HTTPS"
    PROTOCOL_TYPE_FTP = "FTP"
    PROTOCOL_TYPE_SFTP = "SFTP"
    SET_FTP_MODE_PASSIVE = True # True:パッシブモード False:アクティブモード

    def __init__(self, jobnet_id: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
        """
        self.jobnet_id = jobnet_id
        self.logger = GlueLogger(jobnet_id)
        self.s3_client = boto3.client("s3")
        self.secrets_client = boto3.client("secretsmanager")

        # ExternalConnectionインスタンスの生成
        self.ex_con = ExCon()


    def get_connection_info(self, secret_name: str) -> Dict[str, Any]:
        """
        Secrets Managerから外部システム接続情報を取得
        Args:
            secret_name: シークレット名
        Returns:
            Dict: 外部システム接続情報
        """
        try:
            response = self.secrets_client.get_secret_value(SecretId=secret_name)
            return json.loads(response["SecretString"])
        except Exception as e:
            self.logger.error(
                "E_job_get_file_003",
                msg_values=(str(e)),
            )
            self.logger.error("E_job_get_file_002", msg_values=("接続情報取得"))
            raise

    def is_ok_input_param_check(self, connection_info, source_remote_file_full_path):
        """
        必要な外部システム接続情報がそろっているかチェックする
        Args:
            connection_info: 外部システム接続情報
            source_remote_file_full_path: 外部ソースのパス
        Returns:
            Boolean: 必要な接続情報がそろっていればTrue
        """
        connection_param_list = []
        protocol = connection_info.get("protocol")

        # プロトコルごとに必要な接続情報が異なる
        # FTP:"host","username","password"
        if protocol.upper() == self.PROTOCOL_TYPE_FTP:
            connection_param_list = ["host","username","password"]
        # SFTPの場合、パスワード情報と鍵情報のいずれかが設定されているかチェックする。
        elif protocol.upper() == self.PROTOCOL_TYPE_SFTP:
            if not (connection_info.get("password") or connection_info.get("private_key")):
                self.logger.error("E_job_get_file_004", msg_values=("password or private_key"))
                return False
            connection_param_list = ["host","username"]
        # HTTP,HTTPS:"site_url","username","password"
        elif protocol.upper() == self.PROTOCOL_TYPE_HTTP or protocol.upper() == self.PROTOCOL_TYPE_HTTPS:
            connection_param_list = ["site_url","username","password"]

        # 必要な接続情報が存在しない場合、エラーログ出力する
        for param in connection_param_list:
            if not connection_info.get(param):
                self.logger.error("E_job_get_file_004", msg_values=(param))
                return False
        if not source_remote_file_full_path:
            self.logger.error("E_job_get_file_004", msg_values=("source_remote_file_full_path"))
            return False

        return True

    def get_file(
            self,
            connection_info: str,
            source_remote_file_full_path: str,
            multi_file_mode: int,
            backup_file_dir: str,
            merged_files: list
    ):
        """
        ファイル取得
        Args:
            connection_info: 外部システム接続情報
            source_remote_file_full_path: 連携元のファイルパス
            multi_file_mode: 複数ファイル対応モード
            backup_file_dir: バックアップディレクトリ
            merged_files: 連結に利用したファイルリスト（参照渡し）
        Returns:
            str: ファイル内容
        """
        try:
            # 外部システム接続情報からprotocolを取得する
            protocol = connection_info.get("protocol")
            # 取得した接続情報.protocolが「ftp」の場合
            if protocol.upper() == self.PROTOCOL_TYPE_FTP:
                host = connection_info.get("host")
                username = connection_info.get("username")
                password = connection_info.get("password")
                directory = os.path.dirname(source_remote_file_full_path)
                file_name = os.path.basename(source_remote_file_full_path)
                with FTP(host) as ftp:
                    self.ex_con.ftp_login(ftp, username, password, self.SET_FTP_MODE_PASSIVE)
                    ftp.cwd(directory)
                    files = self.ex_con.ftp_nlst(ftp)

                    # 取得するファイルパターンの決定
                    escape_file_name = file_name.replace(".", "\.")  # "."をエスケープするための処理
                    file_pattern = re.compile(fr'{escape_file_name.replace("*", ".*")}', re.IGNORECASE)  # "*"を".*"に変換してパターン化

                    targets = []
                    for file in files:
                        if file_pattern.match(file):
                            targets.append(file)
                    if targets:
                        # 指定のファイルが存在する場合ソート
                        merged_files.clear()
                        for file in sorted(targets):
                            merged_files.append(file)
                            if multi_file_mode == 0:
                                break
                    else:
                        # 指定のファイルが存在しない場合FileNotFound例外終了
                        callback_token = get_callback_token()
                        send_fail_callback_to_step_functions(callback_token, error="FileNotFound", cause="NoFile")
                        raise FileNotFoundException(file_name)

                    combined_content = io.BytesIO()
                    is_first_file = True
                    for file in merged_files:
                        # FTPからファイルをメモリ上に取得
                        temp_buffer = io.BytesIO()
                        self.ex_con.ftp_retrbinary(ftp, file, temp_buffer)
                        temp_buffer.seek(0)

                        # バックアップの取得
                        if backup_file_dir:
                            self.put_s3_file(os.path.join(backup_file_dir, file), temp_buffer.read())
                            temp_buffer.seek(0)

                        # バイナリモードでファイルを読み込む
                        lines = temp_buffer.getvalue().splitlines(keepends=True)

                        if multi_file_mode == 2:
                            # 複数ファイル対応モードが2->最初のファイルのヘッダーを保持、それ以降は除外
                            if is_first_file:
                                combined_content.write(b''.join(lines))
                                is_first_file = False
                            else:
                                # 2ファイル目以降はヘッダーを除外
                                combined_content.write(b''.join(lines[1:]))
                        else:
                            # 複数ファイル対応モードが2以外->そのまま連結
                            combined_content.write(b''.join(lines))
                            if multi_file_mode == 0:
                                break

                    combined_content.seek(0)
                    file_content = combined_content.read()
                return file_content

            # 取得した接続情報.protocolが「sftp」の場合
            elif protocol.upper() == self.PROTOCOL_TYPE_SFTP:
                try:
                    host = connection_info.get("host")
                    username = connection_info.get("username")
                    password = connection_info.get("password", "")
                    private_key = connection_info.get("private_key", "")

                    directory = os.path.dirname(source_remote_file_full_path)
                    file_name = os.path.basename(source_remote_file_full_path)
                    transport = paramiko.Transport((host, 22))

                    if password:
                        self.ex_con.transport_connect(transport, username, password=password)
                    else:
                        private_key = private_key.replace('\\\\n', '\n')
                        pkey = paramiko.RSAKey.from_private_key(StringIO(private_key))
                        self.ex_con.transport_connect(transport, username, pkey=pkey)

                    sftp = paramiko.SFTPClient.from_transport(transport)
                    sftp.chdir(directory)
                    files = self.ex_con.sftp_listdir(sftp)

                    # 取得するファイルパターンの決定
                    escape_file_name = file_name.replace(".", "\.")  # "."をエスケープするための処理
                    file_pattern = re.compile(fr'{escape_file_name.replace("*", ".*")}', re.IGNORECASE)  # "*"を".*"に変換してパターン化

                    targets = []
                    for file in files:
                        if file_pattern.match(file):
                            targets.append(file)
                    if targets:
                        # 指定のファイルが存在する場合ソート
                        merged_files.clear()
                        for file in sorted(targets):
                            merged_files.append(file)
                            if multi_file_mode == 0:
                                break
                    else:
                        # 指定のファイルが存在しない場合FileNotFound例外終了
                        callback_token = get_callback_token()
                        send_fail_callback_to_step_functions(callback_token, error="FileNotFound", cause="NoFile")
                        raise FileNotFoundException(file_name)

                    combined_content = io.BytesIO()
                    is_first_file = True

                    for file in merged_files:
                        # SFTPからファイルをメモリ上に取得
                        temp_buffer = io.BytesIO()
                        self.ex_con.sftp_getfo(sftp, file, temp_buffer)
                        temp_buffer.seek(0)

                        # バックアップの取得
                        if backup_file_dir:
                            self.put_s3_file(os.path.join(backup_file_dir, file), temp_buffer.read())
                            temp_buffer.seek(0)

                        # バイナリモードでファイルを読み込む
                        lines = temp_buffer.getvalue().splitlines(keepends=True)
                        if multi_file_mode == 2:
                            # 複数ファイル対応モードが2->最初のファイルのヘッダーを保持、それ以降は除外
                            if is_first_file:
                                combined_content.write(b''.join(lines))
                                is_first_file = False
                            else:
                                # 2ファイル目以降はヘッダーを除外
                                combined_content.write(b''.join(lines[1:]))
                        else:
                            # 複数ファイル対応モードが2以外->そのまま連結
                            combined_content.write(b''.join(lines))

                    combined_content.seek(0)
                    file_content = combined_content.read()
                except Exception as e:
                    raise e
                finally:
                    if sftp is not None:
                        sftp.close()
                    if transport is not None:
                        transport.close()

                return file_content

            # 取得した接続情報.protocolが「https」または「http」の場合
            elif (
                protocol.upper() == self.PROTOCOL_TYPE_HTTPS
                or protocol.upper() == self.PROTOCOL_TYPE_HTTP
            ):
                site_url = connection_info.get("site_url")
                username = connection_info.get("username")
                password = connection_info.get("password", "")

                # SharePointからファイルをダウンロード
                ctx = ClientContext(site_url).with_credentials(
                    UserCredential(username, password)
                )
                file = ctx.web.get_file_by_server_relative_url(
                    source_remote_file_full_path
                )

                # バイトストリームを準備
                file_content = BytesIO()

                # ファイルをバイトストリームにダウンロード
                file.download(file_content).execute_query()
                return file_content
            else:
                raise ValueError(f"There were no protocol: '{protocol}' cases")

        except BaseException as e:
            self.logger.error(
                "E_job_get_file_003",
                msg_values=(str(e)),
            )
            method_name = f"{connection_info.get('protocol')}".upper() + "ファイル取得"
            self.logger.error(
                "E_job_get_file_002",
                msg_values=(method_name),
            )
            raise e

    def put_s3_file(self, s3_storage_file_full_path, input_data_file_get):
        """
        S3ファイル配置
        Args:
            s3_storage_file_full_path: S3ストレージファイルパス
            input_data_file_get: ファイル内容（連携元から取得）
        """

        def _put():
            self.s3_client.put_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=s3_storage_file_full_path,
                Body=input_data_file_get,
            )

        method_name = "S3ファイル配置"
        retry_function(
            _put,
            self.logger,
            method_name,
            "I_job_get_file_003",
            "E_job_get_file_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
            retry_interval=int(os.environ.get("S3_RETRY_INTERVAL")),
        )

    def backup_input_file(
        self, s3_storage_file_full_path: str, backup_file_dir: str, file_name: str
    ):
        """
        インプットファイルバックアップ
        Args:
            s3_storage_file_full_path: S3ストレージファイルパス
            backup_file_dir: バックアップファイルディレクトリ
            file_name: 連携元のファイルパス[ファイル名]
        """

        def _copy():
            bucket_name = os.environ.get("S3_BUCKET_NAME")
            self.s3_client.copy_object(
                Bucket=bucket_name,
                Key=backup_file_dir + file_name,
                CopySource={"Bucket": bucket_name, "Key": s3_storage_file_full_path},
            )

        method_name = "インプットファイルバックアップ"
        retry_function(
            _copy,
            self.logger,
            method_name,
            "I_job_get_file_003",
            "E_job_get_file_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
            retry_interval=int(os.environ.get("S3_RETRY_INTERVAL")),
        )

    def delete_input_file(
        self, connection_info: str, source_remote_file_full_path: str, merged_files: list
    ):
        """
        インプットファイル削除
        Args:
            connection_info: 外部システム接続情報
            source_remote_file_full_path: 連携元のファイルパス
            multi_file_mode: 複数ファイル対応モード
            merged_files: 取得したファイルのリスト
        """

        try:
            # 外部システム接続情報からprotocolを取得する
            protocol = connection_info.get("protocol")
            # 取得した接続情報.protocolが「ftp」の場合
            if protocol.upper() == self.PROTOCOL_TYPE_FTP:
                host = connection_info.get("host")
                username = connection_info.get("username")
                password = connection_info.get("password", "")

                # 取得するファイルパターンの決定
                directory = os.path.dirname(source_remote_file_full_path)

                with FTP(host) as ftp:
                    self.ex_con.ftp_login(ftp, username, password, self.SET_FTP_MODE_PASSIVE)
                    ftp.cwd(directory)
                    for file in merged_files:
                        self.ex_con.ftp_delete(ftp, file)

            # 取得した接続情報.protocolが「sftp」の場合
            elif protocol.upper() == self.PROTOCOL_TYPE_SFTP:
                try:
                    # 取得するファイルパターンの決定
                    directory = os.path.dirname(source_remote_file_full_path)

                    hostname = connection_info.get("host")
                    username = connection_info.get("username")
                    password = connection_info.get("password", "")
                    private_key = connection_info.get("private_key", "")

                    # ParamikoでSSHクライアントを作成
                    client = paramiko.SSHClient()
                    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

                    # 認証情報を使用して接続
                    if password:
                        self.ex_con.connect(client, hostname, username, password=password)
                    else:
                        # 秘密鍵を使用する場合
                        private_key = private_key.replace('\\\\n', '\n')
                        pkey = paramiko.RSAKey.from_private_key(StringIO(private_key))
                        self.ex_con.connect(client, hostname, username, pkey=pkey)

                    # SFTPセッションを開始
                    sftp = self.ex_con.open_sftp(client)
                    sftp.chdir(directory)
                    for file in merged_files:
                        self.ex_con.sftp_remove(
                            sftp, file
                            )  # 指定されたファイルを削除

                except Exception as e:
                    raise e
                finally:
                    if sftp is not None:
                        sftp.close()
                    if client is not None:
                        client.close()

            # 取得した接続情報.protocolが「https」または「http」の場合
            elif (
                protocol.upper() == self.PROTOCOL_TYPE_HTTPS
                or protocol.upper() == self.PROTOCOL_TYPE_HTTP
            ):
                site_url = connection_info.get("site_url")
                username = connection_info.get("username")
                password = connection_info.get("password", "")
                private_key = connection_info.get("private_key", "")

                # SharePointへの接続を確立
                ctx = ClientContext(site_url).with_credentials(
                    UserCredential(username, password)
                )

                # 指定されたファイルを取得し、削除する
                file = ctx.web.get_file_by_server_relative_url(
                    source_remote_file_full_path
                )
                file.delete_object().execute_query()
            else:
                raise ValueError(f"There were no protocol: '{protocol}' cases")

        except BaseException as e:
            self.logger.error(
                "E_job_get_file_003",
                msg_values=(str(e)),
            )
            method_name = f"{connection_info.get('protocol')}".upper() + "ファイル削除"
            self.logger.error(
                "E_job_get_file_002",
                msg_values=(method_name),
            )
            raise

    def execute(self, params: Dict[str, Any]):
        """
        メイン処理
        Args:
            params: 実行パラメータ
                - source_secret_name: Secrets Managerシークレット名
                - source_remote_file_full_path: 連携元のファイルパス
                - file_name: 連携元のファイルパス[ファイル名]
                - output_file_dir: S3ストレージファイルパス
                - backup_flag: バックアップフラグ
                - backup_file_dir: バックアップファイルディレクトリ
                - jobnet_id: ジョブネットID
                - multi_file_mode: 複数ファイル対応モード
        """
        try:
            self.params = params
            source_remote_file_full_path = params["source_remote_file_full_path"]
            s3_storage_file_full_path = params["s3_storage_file_full_path"]
            file_name = os.path.basename(params["source_remote_file_full_path"])
            backup_file_dir = params["backup_file_dir"]
            multi_file_mode = params["multi_file_mode"]

            # 2.4.2 開始ログ出力
            self.logger.info(
                "I_job_get_file_001",
                msg_values=(file_name),
            )
            self.logger.info(
                "I_job_get_file_005",
                (
                    multi_file_mode,
                    s3_storage_file_full_path
                ),
            )

            # 2.4.3 外部システム接続情報取得
            connection_info = self.get_connection_info(params["source_secret_name"])
            # 外部システム接続情報が正しく取得できていない場合、例外を送出
            if not self.is_ok_input_param_check(connection_info, source_remote_file_full_path):
                raise ValueError(
                    "There were no value"
                )

            # 2.4.4 ファイル取得
            merged_files = []  # 取得したファイル名のリスト、削除で利用
            input_data_file_get = self.get_file(
                connection_info,
                source_remote_file_full_path,
                multi_file_mode,
                backup_file_dir if params["backup_flag"] else "",
                merged_files
            )
            # 2.4.5.連携元ファイル名をログに出力
            self.logger.info(
                "I_job_get_file_006",
                ", ".join(merged_files)
            )

            protocol = connection_info.get("protocol")
            if (not merged_files and
                (protocol.upper() == self.PROTOCOL_TYPE_FTP or
                    protocol.upper() == self.PROTOCOL_TYPE_SFTP)):
                # 対象ファイルがない場合ログ出力し、2.4.8 終了処理へ進む
                self.logger.info(
                    "I_job_get_file_004",
                    msg_values=(file_name),
                )
                return
            # 2.4.6 S3ファイル配置
            self.put_s3_file(s3_storage_file_full_path, input_data_file_get)

            # 2.4.7 インプットファイルバックアップ
            if (params["backup_flag"] and
                (protocol.upper() == self.PROTOCOL_TYPE_HTTPS or
                    protocol.upper() == self.PROTOCOL_TYPE_HTTP)):
                self.backup_input_file(
                    s3_storage_file_full_path,
                    backup_file_dir,
                    merged_files[0] if merged_files else file_name
                )

            # 2.4.8 インプットファイル削除
            self.delete_input_file(
                connection_info,
                source_remote_file_full_path,
                merged_files
            )

            # 2.4.9 終了処理
            self.logger.info(
                "I_job_get_file_002",
                msg_values=(merged_files[0] if merged_files else file_name),
            )
        except Exception as e:
            # 2.4.10 例外処理
            self.logger.error(
                "E_job_get_file_003",
                msg_values=(str(e)),
            )
            self.logger.error(
                "E_job_get_file_001",
                msg_values=(os.path.basename(params["source_remote_file_full_path"]),),
            )
            raise e


def get_params():
    # 2.4.1 パラメータ取得
    params = get_job_params()

    # 必須パラメータのチェック
    required_params = [
        "source_secret_name",  # Secrets Managerシークレット名
        "source_remote_file_full_path",  # 連携元のファイルパス
        "s3_storage_file_full_path",  # S3ストレージファイルパス
        "jobnet_id",  # ジョブネットID
    ]

    for param in required_params:
        if param not in params:
            raise ValueError(f"Required parameter '{param}' is missing")

    # 任意パラメータのデフォルト値設定
    params["backup_flag"] = str_to_bool(params.get("backup_flag", "False"))  # バックアップフラグ
    params["multi_file_mode"] = int(params.get("multi_file_mode",'0')) # 複数ファイル対応モード

    # 入力チェック
    # バックアップフラグTrueの場合、バックアップファイルディレクトリがない場合はエラーとする
    if params.get("backup_flag") and not params.get("backup_file_dir"):
        raise ValueError(f"Required parameter 'backup_file_dir' is missing")

    return params


# メイン処理
def main():
    """メイン関数"""
    try:
        # 環境変数の初期化
        initialize_env()

        # 2.4.1 パラメータ取得
        params = get_params()

        # ジョブの実行
        job = GlueJobGetFile(params["jobnet_id"])
        job.execute(params)
    except Exception as e:
        print(f"Error occurred: {str(e)}")
        raise e

if __name__ == "__main__":
    main()

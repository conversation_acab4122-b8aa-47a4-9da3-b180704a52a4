[INFO]
I_job_api_to_file_crm_001 = ジョブを開始しました。(ファイル名=%%s)
I_job_api_to_file_crm_002 = ジョブが正常終了しました。(ファイル名=%%s)
I_job_api_to_file_crm_003 = 処理をリトライしました。(処理名=%%s)
I_job_api_to_file_crm_004 = APIを開始します。(リクエストパラメータ=%%s)
I_job_api_to_file_crm_005 = APIが完了しました。(HTTPステータス=%%s)
I_job_api_to_file_crm_006 = 差分連携フラグ:%%s 前回同期済みタイムスタンプ:%%s 出力ファイル:%%s
I_job_bulk_api_register_001 = ジョブを開始しました。(ファイル名=%%s)
I_job_bulk_api_register_002 = ジョブが正常終了しました。(ファイル名=%%s)
I_job_bulk_api_register_003 = 処理をリトライしました。(処理名=%%s)
I_job_bulk_api_register_004 = APIを開始します。(API名=%%s,リクエストパラメータ=%%s)
I_job_bulk_api_register_005 = APIが完了しました。(API名=%%s,HTTPステータス=%%s)
I_job_bulk_api_register_006 = 取得対象ファイルは%%sです。
I_job_bulk_api_register_007 = %%sは0byteです。
I_job_convert_character_encoding_001 = ジョブを開始しました。(ファイル名=%%s)
I_job_convert_character_encoding_002 = ジョブが正常終了しました。(ファイル名=%%s)
I_job_convert_character_encoding_003 = 処理をリトライしました。(処理名=%%s)
I_job_convert_character_encoding_004 = 入力ファイル:%%s 出力ファイル:%%s 変換前文字コード:%%s 変換後文字コード:%%s
I_job_db_to_file_001 = ジョブを開始しました。(ファイル名=%%s)
I_job_db_to_file_002 = ジョブが正常終了しました。(ファイル名=%%s)
I_job_db_to_file_003 = 処理をリトライしました。(処理名=%%s)
I_job_db_to_file_004 = 抽出SQL：%%s 前回同期済みタイムスタンプ：%%s 差分基準時刻：%%s 出力ファイル：%%s
I_job_convert_format_001 = ジョブを開始しました。(ファイル名=%%s)
I_job_convert_format_002 = ジョブが正常終了しました。(ファイル名=%%s)
I_job_convert_format_003 = 処理をリトライしました。(処理名=%%s)
I_job_convert_format_004 = 入力ファイル:%%s 出力ファイル:%%s
I_job_internal_db_import_001 = ジョブを開始しました。(ファイル名=%%s)
I_job_internal_db_import_002 = ジョブが正常終了しました。(ファイル名=%%s)
I_job_internal_db_import_003 = 処理をリトライしました。(処理名=%%s)
I_job_internal_db_import_004 = データが存在しないためインポートが行われませんでした。(ファイル名=%%s)
I_job_internal_db_import_005 = インポートファイル：%%s
I_job_get_file_001 = ジョブを開始しました。(ファイル名=%%s)
I_job_get_file_002 = ジョブが正常終了しました。(ファイル名=%%s)
I_job_get_file_003 = 処理をリトライしました。(処理名=%%s)
I_job_get_file_004 = 対象ファイルがありませんでした。(ファイル名=%%s)
I_job_get_file_005 = 複数ファイル対応モード：%%s S3配置先：%%s
I_job_get_file_006 = 連携元ファイル：%%s
I_job_send_file_001 = ジョブを開始しました。(ファイル名=%%s)
I_job_send_file_002 = ジョブが正常終了しました。(ファイル名=%%s)
I_job_send_file_003 = 処理をリトライしました。(処理名=%%s)
I_job_send_file_004 = 連携元ファイル:%%s,連携先ファイル:%%s
I_job_db_delete_001 = ジョブを開始しました。(テーブル名=%%s)
I_job_db_delete_002 = ジョブが正常終了しました。(テーブル名=%%s)
I_job_db_delete_003 = 対象テーブル：%%s 実行SQL：truncate
I_job_db_delete_004 = 対象テーブル：%%s 実行SQL：delete 開始日時：%%s 終了日時：%%s
I_job_file_compress_001 = ジョブを開始しました。(ファイル名=%%s)
I_job_file_compress_002 = ジョブが正常終了しました。(ファイル名=%%s)
I_job_file_compress_003 = 処理をリトライしました。(処理名=%%s)
I_job_file_compress_004 = 入力ファイル:%%s 出力ファイル:%%s
I_job_execute_sql_001 = ジョブを開始しました。
I_job_execute_sql_002 = ジョブが正常終了しました。
I_job_execute_sql_003 = 前回同期時刻取得ファイル名:%%s 前回同期時刻:%%s
I_job_execute_sql_004 = 差分基準時刻を取得するSQLクエリ_ID:%%s 差分基準時刻:%%s
I_job_db_to_file_xml_count_001 = %%s:%%s件

[WARN]
W_sample_001 = this is a message sample.

[ERROR]
E_job_api_to_file_crm_001 = ジョブが異常終了しました。(ファイル名=%%s)
E_job_api_to_file_crm_002 = 処理で異常が発生しました。(処理名=%%s)
E_job_api_to_file_crm_003 = 例外発生しました。%%s
E_job_api_to_file_crm_004 = APIでエラーを検知しました。%%s
E_job_bulk_api_register_001 = ジョブが異常終了しました。(ファイル名=%%s)
E_job_bulk_api_register_002 = 処理で異常が発生しました。(処理名=%%s)
E_job_bulk_api_register_003 = 例外発生しました。%%s
E_job_convert_character_encoding_001 = ジョブが異常終了しました。(ファイル名=%%s)
E_job_convert_character_encoding_002 = 処理で異常が発生しました。(処理名=%%s)
E_job_convert_character_encoding_003 = 例外発生しました。%%s
E_job_db_to_file_001 = ジョブが異常終了しました。(ファイル名=%%s)
E_job_db_to_file_002 = 処理で異常が発生しました。(処理名=%%s)
E_job_db_to_file_003 = 例外発生しました。%%s
E_job_convert_format_001 = ジョブが異常終了しました。(ファイル名=%%s)
E_job_convert_format_002 = 処理で異常が発生しました。(処理名=%%s)
E_job_convert_format_003 = 例外発生しました。%%s
E_job_internal_db_import_001 = ジョブが異常終了しました。(ファイル名=%%s)
E_job_internal_db_import_002 = 処理で異常が発生しました。(処理名=%%s)
E_job_internal_db_import_003 = 例外発生しました。%%s
E_job_internal_db_import_004 = インポートデータのデコードに失敗しました。(ファイル名=%%s)
E_job_get_file_001 = ジョブが異常終了しました。(ファイル名=%%s)
E_job_get_file_002 = 処理で異常が発生しました。(処理名=%%s)
E_job_get_file_003 = 例外発生しました。%%s
E_job_get_file_004 = 必要な接続情報が存在しませんでした。(パラメータ名=%%s)
E_job_send_file_001 = ジョブが異常終了しました。(ファイル名=%%s)
E_job_send_file_002 = 処理で異常が発生しました。(処理名=%%s)
E_job_send_file_003 = 例外発生しました。%%s
E_job_db_delete_001 = ジョブが異常終了しました。(テーブル名=%%s)
E_job_db_delete_002 = 処理で異常が発生しました。(処理名=%%s)
E_job_db_delete_003 = 例外発生しました。%%s
E_job_file_compress_001 = ジョブが異常終了しました。(ファイル名=%%s)
E_job_file_compress_002 = 処理で異常が発生しました。(処理名=%%s)
E_job_file_compress_003 = 例外発生しました。%%s
E_job_execute_sql_001 = ジョブが異常終了しました。
E_job_execute_sql_002 = 例外発生しました。%%s
E_job_execute_sql_003 = SQLの実行に失敗しました。%%s
E_file_not_found_001 = 指定されたファイルが見つかりませんでした。ファイル名：%%s

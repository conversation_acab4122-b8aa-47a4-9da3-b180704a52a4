WITH linkage_target_products AS (
  -- 商品連携マスタと期間別価格連携マスタで更新日時が前回差分取得日時以降、または、期間別価格連携マスタの適用開始日が処理日である商品一覧を取得
  SELECT DISTINCT
    pl.MDM_INTEGRATION_MANAGEMENT_CD
  FROM
    mdm.product_linkage pl
    LEFT JOIN mdm.period_price_linkage ppl ON pl.mdm_integration_management_cd = ppl.mdm_integration_management_cd
    AND ppl.mdm_integration_management_cd_nk IS NOT NULL
  WHERE
    (
      (
        pl.PMS_U_YMD > :sync_datetime
        AND pl.PMS_U_YMD <= :diff_base_timestamp
      )
      OR TO_CHAR(ppl.apply_start_date, 'YYYYMMDD') = TO_CHAR(:diff_base_timestamp, 'YYYYMMDD')
    )
    AND (
       pl.PRODUCT_TYPE < '20'
       OR pl.PRODUCT_TYPE > '29'
    )
    AND (
       pl.PERIOD_SET_SALES_CHANNEL_1 = '10'
       OR pl.PERIOD_SET_SALES_CHANNEL_2 = '10'
       OR pl.PERIOD_SET_SALES_CHANNEL_3 = '10'
    )
    AND pl.MAIL_ORDER_PRODUCT_CD IS NOT NULL
),
linkage_target_price AS (
  SELECT
    pl.MDM_INTEGRATION_MANAGEMENT_CD,
    COALESCE(ppl2.apply_start_date, ppl4.apply_start_date) AS apply_start_date -- ①があれば採用、なければ②を採用
  FROM
    linkage_target_products pl
    LEFT JOIN (
      -- ①処理日より前で、直近の価格
      SELECT
        mdm_integration_management_cd,
        max(apply_start_date) AS apply_start_date
      FROM
        mdm.period_price_linkage AS ppl3
      WHERE
        ppl3.mdm_integration_management_cd_nk IS NOT NULL
        AND ppl3.apply_start_date <= :diff_base_timestamp
      GROUP BY
        mdm_integration_management_cd
    ) AS ppl2 ON pl.mdm_integration_management_cd = ppl2.mdm_integration_management_cd
    LEFT JOIN (
      -- ②処理日より後で、直近の価格
      SELECT
        mdm_integration_management_cd,
        min(apply_start_date) AS apply_start_date
      FROM
        mdm.period_price_linkage AS ppl3
      WHERE
        ppl3.mdm_integration_management_cd_nk IS NOT NULL
        AND ppl3.apply_start_date > :diff_base_timestamp
      GROUP BY
        mdm_integration_management_cd
    ) AS ppl4 ON pl.mdm_integration_management_cd = ppl4.mdm_integration_management_cd
),
edit_target_products AS (
  -- 商品編集マスタと期間別価格編集マスタで更新日時が前回差分取得日時以降、または、期間別価格編集マスタの適用開始日が処理日である商品一覧を取得
  SELECT DISTINCT
    pe.MDM_INTEGRATION_MANAGEMENT_CD
  FROM
    mdm.product_edit pe
    LEFT JOIN mdm.period_price_edit ppe ON pe.mdm_integration_management_cd = ppe.mdm_integration_management_cd
    AND ppe.mdm_integration_management_cd_nk IS NOT NULL
  WHERE
    pe.COMPOSITION_OMS_LINK_FLG = '1'
    AND NOT EXISTS (
      SELECT
        1
      FROM
        mdm.product_linkage pl_inner
      WHERE
        pl_inner.mdm_integration_management_cd = pe.mdm_integration_management_cd
    )
    AND (
      (
        pe.PMS_U_YMD > :sync_datetime
        AND pe.PMS_U_YMD <= :diff_base_timestamp
      )
      OR TO_CHAR(ppe.apply_start_date, 'YYYYMMDD') = TO_CHAR(:diff_base_timestamp, 'YYYYMMDD')
    )
    AND (
      pe.PRODUCT_TYPE < '20'
      OR pe.PRODUCT_TYPE > '29'
    )
    AND (
      pe.PERIOD_SET_SALES_CHANNEL_1 = '10'
      OR pe.PERIOD_SET_SALES_CHANNEL_2 = '10'
      OR pe.PERIOD_SET_SALES_CHANNEL_3 = '10'
    )
    AND pe.MAIL_ORDER_PRODUCT_CD IS NOT NULL
),
edit_target_price AS (
  SELECT
    pe.MDM_INTEGRATION_MANAGEMENT_CD,
    COALESCE(ppe2.apply_start_date, ppe4.apply_start_date) AS apply_start_date -- ①があれば採用、なければ②を採用
  FROM
    edit_target_products pe
    LEFT JOIN (
      -- ①処理日より前で、直近の価格
      SELECT
        mdm_integration_management_cd,
        max(apply_start_date) AS apply_start_date
      FROM
        mdm.period_price_edit AS ppe3
      WHERE
        ppe3.mdm_integration_management_cd_nk IS NOT NULL
        AND ppe3.apply_start_date <= :diff_base_timestamp
      GROUP BY
        mdm_integration_management_cd
    ) AS ppe2 ON pe.mdm_integration_management_cd = ppe2.mdm_integration_management_cd
    LEFT JOIN (
      -- ②処理日より後で、直近の価格
      SELECT
        mdm_integration_management_cd,
        min(apply_start_date) AS apply_start_date
      FROM
        mdm.period_price_edit AS ppe3
      WHERE
        ppe3.mdm_integration_management_cd_nk IS NOT NULL
        AND ppe3.apply_start_date > :diff_base_timestamp
      GROUP BY
        mdm_integration_management_cd
    ) AS ppe4 ON pe.mdm_integration_management_cd = ppe4.mdm_integration_management_cd
),
combined_products AS (
  SELECT
    pl.PRODUCT_TYPE,
    pl.SET_PRODUCT_FLG,
    '0000' AS shop_code,
    pl.MAIL_ORDER_PRODUCT_CD AS commodity_code,
    pl.CORE_PRODUCT_NAME AS commodity_name,
    CASE
      pl.SET_PRODUCT_FLG
      WHEN '1' THEN 1
      ELSE 0
    END AS commodity_type,
    pl.MAIL_ORDER_PRODUCT_CD AS represent_sku_code,
    pl.WAREHOUSE_MANAGEMENT_CD AS hinban_code,
    CASE
      WHEN pl.PRODUCT_TYPE = '11'
      OR pl.SET_PRODUCT_FLG = '1' THEN NULL
      ELSE 0
    END AS stock_status_no,
    CASE
      WHEN pl.SET_PRODUCT_FLG = '1' THEN 4
      WHEN pl.PRODUCT_TYPE = '11' THEN 0
      ELSE 3
    END AS stock_management_type,
    0 AS age_limit_code,
    CASE
      WHEN pl.SET_PRODUCT_FLG = '1' THEN 3
      WHEN pl.PRODUCT_TYPE = '02' OR
        pl.PRODUCT_TYPE = '05' OR pl.PRODUCT_TYPE = '07' OR
        pl.PRODUCT_TYPE = '81' THEN 0
      ELSE ppl.TAX
    END AS commodity_tax_type,
    CASE
      WHEN ppl.TAX = 0 OR pl.PRODUCT_TYPE = '02' OR
        pl.PRODUCT_TYPE = '05' OR pl.PRODUCT_TYPE = '07' OR
        pl.PRODUCT_TYPE = '81' THEN '0'
      WHEN ppl.TAX_RATE = 8 THEN '10000001'
      ELSE '1'
    END AS tax_group_code,
    '' AS short_description,
    '' AS commodity_search_words,
    '' AS prior_printing_description,
    '' AS posterior_printing_description,
    '' AS delivery_description,
    CASE
      WHEN pl.PERIOD_SET_SALES_CHANNEL_1 = '10'
      AND pl.SALES_CHANNEL_1_SALE_START_DATE IS NULL THEN '2000/01/01 00:00:00'
      WHEN pl.PERIOD_SET_SALES_CHANNEL_1 = '10' THEN to_char(
        pl.SALES_CHANNEL_1_SALE_START_DATE,
        'YYYY/MM/DD HH24:MI:SS'
      )
      WHEN pl.PERIOD_SET_SALES_CHANNEL_2 = '10'
      AND pl.SALES_CHANNEL_2_SALE_START_DATE IS NULL THEN '2000/01/01 00:00:00'
      WHEN pl.PERIOD_SET_SALES_CHANNEL_2 = '10' THEN to_char(
        pl.SALES_CHANNEL_2_SALE_START_DATE,
        'YYYY/MM/DD HH24:MI:SS'
      )
      WHEN pl.PERIOD_SET_SALES_CHANNEL_3 = '10'
      AND pl.SALES_CHANNEL_3_SALE_START_DATE IS NULL THEN '2000/01/01 00:00:00'
      WHEN pl.PERIOD_SET_SALES_CHANNEL_3 = '10' THEN to_char(
        pl.SALES_CHANNEL_3_SALE_START_DATE,
        'YYYY/MM/DD HH24:MI:SS'
      )
    END AS sale_start_datetime,
    CASE
      WHEN pl.PERIOD_SET_SALES_CHANNEL_1 = '10'
      AND pl.SALES_CHANNEL_1_SALE_END_DATE IS NULL THEN '2999/12/31 23:59:59'
      WHEN pl.PERIOD_SET_SALES_CHANNEL_1 = '10' THEN to_char(
        pl.SALES_CHANNEL_1_SALE_END_DATE,
        'YYYY/MM/DD HH24:MI:SS'
      )
      WHEN pl.PERIOD_SET_SALES_CHANNEL_2 = '10'
      AND pl.SALES_CHANNEL_2_SALE_END_DATE IS NULL THEN '2999/12/31 23:59:59'
      WHEN pl.PERIOD_SET_SALES_CHANNEL_2 = '10' THEN to_char(
        pl.SALES_CHANNEL_2_SALE_END_DATE,
        'YYYY/MM/DD HH24:MI:SS'
      )
      WHEN pl.PERIOD_SET_SALES_CHANNEL_3 = '10'
      AND pl.SALES_CHANNEL_3_SALE_END_DATE IS NULL THEN '2999/12/31 23:59:59'
      WHEN pl.PERIOD_SET_SALES_CHANNEL_3 = '10' THEN to_char(
        pl.SALES_CHANNEL_3_SALE_END_DATE,
        'YYYY/MM/DD HH24:MI:SS'
      )
    END AS sale_end_datetime,
    '' AS discount_price_start_datetime,
    '' AS discount_price_end_datetime,
    '1970/01/01 09:00:00' AS reservation_start_datetime,
    '1970/01/01 09:00:00' AS reservation_end_datetime,
    '' AS prior_printing_start_date,
    '' AS prior_printing_end_date,
    '' AS posterior_printing_start_date,
    '' AS posterior_printing_end_date,
    0 AS delivery_type_no,
    2 AS sales_method_type,
    '' AS manufacturer_model_no,
    '' AS link_url,
    99999999 AS recommend_commodity_rank,
    99999999 AS commodity_popular_rank,
    '' AS commodity_standard1_name,
    '' AS commodity_standard2_name,
    '' AS commodity_point_rate,
    '' AS commodity_point_start_datetime,
    '' AS commodity_point_end_datetime,
    CASE
      WHEN pl.SALE_STATUS = '0'
      OR pl.SALE_STATUS = '9' THEN '0'
      WHEN pl.SALE_STATUS = '1' THEN pl.SALE_STATUS
    END AS sale_flg,
    0 AS noshi_effective_flg,
    0 AS arrival_goods_flg,
    CASE
      WHEN pl.PRODUCT_TYPE = '05' THEN 0
      ELSE pl.ORDER_PER_ORDER_MAX
    END AS oneshot_order_limit,
    CASE
      WHEN ( pl.PRODUCT_TYPE != '01' AND
           pl.PRODUCT_TYPE != '82' )
      OR pl.SET_PRODUCT_FLG = '1' THEN 0
      ELSE ppl.TAX_EXC
    END AS unit_price,
    0 AS member_price_applied_flg,
    0 AS member_price_discount_rate,
    pl.JAN AS jan_code,
    0 AS stock_threshold,
    0 AS standard_image_type,
    '0' AS purchasing_confirm_flg_pc,
    '0' AS purchasing_confirm_flg_sp,
    pl.PRODUCT_TYPE AS commodity_kind,
    CASE
      WHEN pl.PRODUCT_TYPE NOT IN('02', '07', '81') THEN 0
      ELSE 1
    END AS keihi_hurikae_target_flg,
    '1' AS charge_user_code,
    '' AS commodity_remark,
    CASE
      WHEN pl.PRODUCT_TYPE = '05' OR pl.PRODUCT_TYPE = '81' THEN '1'
      ELSE pl.CALLCENTER
    END AS channel_cc_sale_flg,
    CASE
      WHEN pl.PRODUCT_TYPE = '05'
      OR pl.PRODUCT_TYPE = '81' THEN '0'
      ELSE pl.WEB
    END AS channel_ec_sale_flg,
    1 AS shipping_charge_target_flg,
    0 AS first_purchase_limit_flg,
    CASE
      WHEN pl.PRODUCT_TYPE = '05' OR pl.PRODUCT_TYPE = '81' THEN '0'
      ELSE pl.BUY_SEND_RESERVATION_FLG
    END AS purchase_hold_flg,
    CASE
      pl.PRODUCT_TYPE
      WHEN '05' THEN 1
      ELSE 0
    END AS commodity_exclude_flg,
    pl.SGROUP AS prod_sho_bunrui_cd,
    1 AS pack_calc_pattern,
    0 AS pad_type,
    0 AS fall_down_flg,
    pl.HEIGHT AS height,
    pl.WIDTH AS width,
    pl.DEPTH AS deepness,
    ROUND(pl.WEIGHT, 0) AS weight,
    0 AS tracking_out_flg,
    pl.MDM_INTEGRATION_MANAGEMENT_CD AS mdm_management_code,
    pl.PRODUCT_SEGMENT AS commodity_segment,
    pl.BUSINESS_SEGMENT AS business_segment,
    pl.PRODUCT_CAT AS commodity_group,
    pl.PRODUCT_SERIES AS commodity_series,
    pl.CORE_DEPARTMENT AS core_department,
    pl.ACCOUNTIN_PATTERN_GB AS accounting_pattern_type,
    pl.RETURN_YN AS return_enabled_flg,
    pl.EXCH_YN AS exchange_enabled_flg,
    pl.OUTERBOX_WEIGHT AS exterior_box_weight,
    pl.NEKOPOSU_VOLUME_RATE AS nekoposu_volume_rate,
    pl.WAREHOUSE_ASSEMBLY_SET_PRODUCT_FLG AS warehouse_assembly_flg,
    pl.MAIL_DELIVERY_FLG AS mail_delivery_flg,
    pl.BEFORE_RENEWAL_PRODUCT_NO AS before_renewal_commodity_code,
    pl.MAIN_PRODUCT_NO AS main_product_no,
    pl.PRODUCT_NO AS product_no,
    '' AS orm_rowid,
    '' AS created_user,
    '' AS created_datetime,
    '' AS updated_user,
    '' AS updated_datetime
  FROM
    linkage_target_price t
    INNER JOIN mdm.product_linkage pl ON t.mdm_integration_management_cd = pl.mdm_integration_management_cd
    LEFT JOIN mdm.period_price_linkage ppl ON t.mdm_integration_management_cd = ppl.mdm_integration_management_cd
    AND t.apply_start_date = ppl.apply_start_date
  UNION
  SELECT
    pe.PRODUCT_TYPE,
    pe.SET_PRODUCT_FLG,
    '0000' AS shop_code,
    pe.MAIL_ORDER_PRODUCT_CD AS commodity_code,
    pe.CORE_PRODUCT_NAME AS commodity_name,
    CASE
      pe.SET_PRODUCT_FLG
      WHEN '1' THEN 1
      ELSE 0
    END AS commodity_type,
    pe.MAIL_ORDER_PRODUCT_CD AS represent_sku_code,
    pe.WAREHOUSE_MANAGEMENT_CD AS hinban_code,
    CASE
      WHEN pe.PRODUCT_TYPE = '11'
      OR pe.SET_PRODUCT_FLG = '1' THEN NULL
      ELSE 0
    END AS stock_status_no,
    CASE
      WHEN pe.SET_PRODUCT_FLG = '1' THEN 4
      WHEN pe.PRODUCT_TYPE = '11' THEN 0
      ELSE 3
    END AS stock_management_type,
    0 AS age_limit_code,
    CASE
      WHEN pe.SET_PRODUCT_FLG = '1' THEN 3
      WHEN pe.PRODUCT_TYPE = '02' OR
        pe.PRODUCT_TYPE = '05' OR pe.PRODUCT_TYPE = '07' OR
        pe.PRODUCT_TYPE = '81' THEN 0
      ELSE ppe.TAX
    END AS commodity_tax_type,
    CASE
      WHEN ppe.TAX = 0 OR pe.PRODUCT_TYPE = '02' OR
        pe.PRODUCT_TYPE = '05' OR pe.PRODUCT_TYPE = '07' OR
        pe.PRODUCT_TYPE = '81' THEN '0'
      WHEN ppe.TAX_RATE = 8 THEN '10000001'
      ELSE '1'
    END AS tax_group_code,
    '' AS short_description,
    '' AS commodity_search_words,
    '' AS prior_printing_description,
    '' AS posterior_printing_description,
    '' AS delivery_description,
    CASE
      WHEN PERIOD_SET_SALES_CHANNEL_1 = '10'
      AND SALES_CHANNEL_1_SALE_START_DATE IS NULL THEN '2000/01/01 00:00:00'
      WHEN PERIOD_SET_SALES_CHANNEL_1 = '10' THEN to_char(
        SALES_CHANNEL_1_SALE_START_DATE,
        'YYYY/MM/DD HH24:MI:SS'
      )
      WHEN PERIOD_SET_SALES_CHANNEL_2 = '10'
      AND SALES_CHANNEL_2_SALE_START_DATE IS NULL THEN '2000/01/01 00:00:00'
      WHEN PERIOD_SET_SALES_CHANNEL_2 = '10' THEN to_char(
        SALES_CHANNEL_2_SALE_START_DATE,
        'YYYY/MM/DD HH24:MI:SS'
      )
      WHEN PERIOD_SET_SALES_CHANNEL_3 = '10'
      AND SALES_CHANNEL_3_SALE_START_DATE IS NULL THEN '2000/01/01 00:00:00'
      WHEN PERIOD_SET_SALES_CHANNEL_3 = '10' THEN to_char(
        SALES_CHANNEL_3_SALE_START_DATE,
        'YYYY/MM/DD HH24:MI:SS'
      )
    END AS sale_start_datetime,
    CASE
      WHEN PERIOD_SET_SALES_CHANNEL_1 = '10'
      AND SALES_CHANNEL_1_SALE_END_DATE IS NULL THEN '2999/12/31 23:59:59'
      WHEN PERIOD_SET_SALES_CHANNEL_1 = '10' THEN to_char(
        SALES_CHANNEL_1_SALE_END_DATE,
        'YYYY/MM/DD HH24:MI:SS'
      )
      WHEN PERIOD_SET_SALES_CHANNEL_2 = '10'
      AND SALES_CHANNEL_2_SALE_END_DATE IS NULL THEN '2999/12/31 23:59:59'
      WHEN PERIOD_SET_SALES_CHANNEL_2 = '10' THEN to_char(
        SALES_CHANNEL_2_SALE_END_DATE,
        'YYYY/MM/DD HH24:MI:SS'
      )
      WHEN PERIOD_SET_SALES_CHANNEL_3 = '10'
      AND SALES_CHANNEL_3_SALE_END_DATE IS NULL THEN '2999/12/31 23:59:59'
      WHEN PERIOD_SET_SALES_CHANNEL_3 = '10' THEN to_char(
        SALES_CHANNEL_3_SALE_END_DATE,
        'YYYY/MM/DD HH24:MI:SS'
      )
    END AS sale_end_datetime,
    '' AS discount_price_start_datetime,
    '' AS discount_price_end_datetime,
    '1970/01/01 09:00:00' AS reservation_start_datetime,
    '1970/01/01 09:00:00' AS reservation_end_datetime,
    '' AS prior_printing_start_date,
    '' AS prior_printing_end_date,
    '' AS posterior_printing_start_date,
    '' AS posterior_printing_end_date,
    0 AS delivery_type_no,
    2 AS sales_method_type,
    '' AS manufacturer_model_no,
    '' AS link_url,
    99999999 AS recommend_commodity_rank,
    99999999 AS commodity_popular_rank,
    '' AS commodity_standard1_name,
    '' AS commodity_standard2_name,
    '' AS commodity_point_rate,
    '' AS commodity_point_start_datetime,
    '' AS commodity_point_end_datetime,
    CASE
      WHEN SALE_STATUS = '0'
      OR SALE_STATUS = '9' THEN '0'
      WHEN SALE_STATUS = '1' THEN SALE_STATUS
    END AS sale_flg,
    0 AS noshi_effective_flg,
    0 AS arrival_goods_flg,
    CASE
      WHEN pe.PRODUCT_TYPE = '05' THEN 0
      ELSE ORDER_PER_ORDER_MAX
    END AS oneshot_order_limit,
    CASE
      WHEN ( pe.PRODUCT_TYPE != '01' AND
          pe.PRODUCT_TYPE != '82' )
      OR pe.SET_PRODUCT_FLG = '1' THEN 0
      ELSE ppe.TAX_EXC
    END AS unit_price,
    0 AS member_price_applied_flg,
    0 AS member_price_discount_rate,
    JAN AS jan_code,
    0 AS stock_threshold,
    0 AS standard_image_type,
    '0' AS purchasing_confirm_flg_pc,
    '0' AS purchasing_confirm_flg_sp,
    pe.PRODUCT_TYPE AS commodity_kind,
    CASE
      WHEN pe.PRODUCT_TYPE NOT IN('02', '07', '81') THEN 0
      ELSE 1
    END AS keihi_hurikae_target_flg,
    '1' AS charge_user_code,
    '' AS commodity_remark,
    CASE
      WHEN pe.PRODUCT_TYPE = '05' OR pe.PRODUCT_TYPE = '81' THEN '1'
      ELSE pe.CALLCENTER
    END AS channel_cc_sale_flg,
    CASE
      WHEN pe.PRODUCT_TYPE = '05'
      OR pe.PRODUCT_TYPE = '81' THEN '0'
      ELSE WEB
    END AS channel_ec_sale_flg,
    1 AS shipping_charge_target_flg,
    0 AS first_purchase_limit_flg,
    CASE
      WHEN pe.PRODUCT_TYPE = '05' OR pe.PRODUCT_TYPE = '81' THEN '0'
      ELSE pe.BUY_SEND_RESERVATION_FLG
    END AS purchase_hold_flg,
    CASE
      pe.PRODUCT_TYPE
      WHEN '05' THEN 1
      ELSE 0
    END AS commodity_exclude_flg,
    pe.SGROUP AS prod_sho_bunrui_cd,
    1 AS pack_calc_pattern,
    0 AS pad_type,
    0 AS fall_down_flg,
    pe.HEIGHT AS height,
    pe.WIDTH AS width,
    pe.DEPTH AS deepness,
    ROUND(WEIGHT, 0) AS weight,
    0 AS tracking_out_flg,
    pe.MDM_INTEGRATION_MANAGEMENT_CD AS mdm_management_code,
    pe.PRODUCT_SEGMENT AS commodity_segment,
    pe.BUSINESS_SEGMENT AS business_segment,
    pe.PRODUCT_CAT AS commodity_group,
    pe.PRODUCT_SERIES AS commodity_series,
    pe.CORE_DEPARTMENT AS core_department,
    pe.ACCOUNTIN_PATTERN_GB AS accounting_pattern_type,
    pe.RETURN_YN AS return_enabled_flg,
    pe.EXCH_YN AS exchange_enabled_flg,
    pe.OUTERBOX_WEIGHT AS exterior_box_weight,
    pe.NEKOPOSU_VOLUME_RATE AS nekoposu_volume_rate,
    pe.WAREHOUSE_ASSEMBLY_SET_PRODUCT_FLG AS warehouse_assembly_flg,
    pe.MAIL_DELIVERY_FLG AS mail_delivery_flg,
    pe.BEFORE_RENEWAL_PRODUCT_NO AS before_renewal_commodity_code,
    pe.MAIN_PRODUCT_NO AS main_product_no,
    pe.PRODUCT_NO AS product_no,
    '' AS orm_rowid,
    '' AS created_user,
    '' AS created_datetime,
    '' AS updated_user,
    '' AS updated_datetime
  FROM
    edit_target_price t
    INNER JOIN mdm.product_edit pe ON t.mdm_integration_management_cd = pe.mdm_integration_management_cd
    LEFT JOIN mdm.period_price_edit ppe ON t.mdm_integration_management_cd = ppe.mdm_integration_management_cd
    AND t.apply_start_date = ppe.apply_start_date
)
SELECT
  a.shop_code,
  a.commodity_code,
  a.commodity_name,
  a.commodity_type,
  a.represent_sku_code,
  a.hinban_code,
  a.stock_status_no,
  a.stock_management_type,
  a.age_limit_code,
  a.commodity_tax_type,
  a.tax_group_code,
  a.short_description,
  a.commodity_search_words,
  a.prior_printing_description,
  a.posterior_printing_description,
  a.delivery_description,
  a.sale_start_datetime,
  a.sale_end_datetime,
  a.discount_price_start_datetime,
  a.discount_price_end_datetime,
  a.reservation_start_datetime,
  a.reservation_end_datetime,
  a.prior_printing_start_date,
  a.prior_printing_end_date,
  a.posterior_printing_start_date,
  a.posterior_printing_end_date,
  a.delivery_type_no,
  a.sales_method_type,
  a.manufacturer_model_no,
  a.link_url,
  a.recommend_commodity_rank,
  a.commodity_popular_rank,
  a.commodity_standard1_name,
  a.commodity_standard2_name,
  a.commodity_point_rate,
  a.commodity_point_start_datetime,
  a.commodity_point_end_datetime,
  a.sale_flg,
  a.noshi_effective_flg,
  a.arrival_goods_flg,
  a.oneshot_order_limit,
  a.unit_price,
  a.member_price_applied_flg,
  a.member_price_discount_rate,
  a.jan_code,
  a.stock_threshold,
  a.standard_image_type,
  a.purchasing_confirm_flg_pc,
  a.purchasing_confirm_flg_sp,
  a.commodity_kind,
  a.keihi_hurikae_target_flg,
  a.charge_user_code,
  a.commodity_remark,
  a.channel_cc_sale_flg,
  a.channel_ec_sale_flg,
  a.shipping_charge_target_flg,
  a.first_purchase_limit_flg,
  a.purchase_hold_flg,
  a.commodity_exclude_flg,
  a.prod_sho_bunrui_cd,
  a.pack_calc_pattern,
  a.pad_type,
  a.fall_down_flg,
  a.height,
  a.width,
  a.deepness,
  a.weight,
  a.tracking_out_flg,
  a.mdm_management_code,
  a.commodity_segment,
  a.business_segment,
  a.commodity_group,
  a.commodity_series,
  a.core_department,
  a.accounting_pattern_type,
  a.return_enabled_flg,
  a.exchange_enabled_flg,
  a.exterior_box_weight,
  a.nekoposu_volume_rate,
  a.warehouse_assembly_flg,
  a.mail_delivery_flg,
  a.before_renewal_commodity_code,
  a.main_product_no,
  a.product_no,
  a.orm_rowid,
  a.created_user,
  a.created_datetime,
  a.updated_user,
  a.updated_datetime
FROM combined_products a

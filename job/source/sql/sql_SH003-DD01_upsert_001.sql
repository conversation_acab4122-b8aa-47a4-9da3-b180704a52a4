-- SQL 1: <PERSON>LE<PERSON> Query
DELETE FROM member_purchased_products mpp
WHERE EXISTS (
    SELECT 1
    FROM order_detail_view od
    WHERE mpp.order_no = od.order_no
      AND mpp.order_detail_no = od.order_detail_no
      AND od.delete_flg = 1
);


-- Insert or update member_purchased_products based on order_header and order_detail
INSERT INTO member_purchased_products (
    customer_code,
    order_no,
    order_detail_no,
    commodity_code,
    order_date,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    oh.neo_customer_no,
    oh.order_no,
    od.order_detail_no,
    od.commodity_code,
    oh.order_datetime,
    'JN_SH003-DF01_001',
    NOW(),
    'JN_SH003-DF01_001',
    NOW(),
    1
FROM order_header oh
INNER JOIN order_detail_view od ON oh.order_no = od.order_no
WHERE od.delete_flg = 0
    AND oh.order_no = od.order_no
ON CONFLICT (order_no, order_detail_no)
DO UPDATE SET
    customer_code = EXCLUDED.customer_code,
    commodity_code = EXCLUDED.commodity_code,
    order_date = EXCLUDED.order_date,
    d_updated_user = 'JN_SH003-DF01_001',
    d_updated_datetime = NOW(),
    d_version = member_purchased_products.d_version + 1;


-- Insert or update member_purchased_products based on sales_info_alignment tables
INSERT INTO member_purchased_products (
    customer_code,
    order_no,
    order_detail_no,
    commodity_code,
    order_date,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    siah.neo_customer_no,
    siah.v_order_no,
    siad.line_num,
    siad.item_cd,
    siah.business_date,
    'JN_SH003-DF01_001',
    NOW(),
    'JN_SH003-DF01_001',
    NOW(),
    1
FROM sales_info_alignment_header siah
INNER JOIN sales_info_alignment_detail siad ON siah.shop_cd = siad.shop_cd
    AND siah.business_date = siad.business_date
    AND siah.register_num = siad.register_num
    AND siah.receipt_num = siad.receipt_num
WHERE siad.item_num IS NOT NULL
ON CONFLICT (order_no, order_detail_no)
DO UPDATE SET
    customer_code = EXCLUDED.customer_code,
    commodity_code = EXCLUDED.commodity_code,
    order_date = EXCLUDED.order_date,
    d_updated_user = 'JN_SH003-DF01_001',
    d_updated_datetime = NOW(),
    d_version = member_purchased_products.d_version + 1;

# AWS 本番環境 CI/CD 構築手順書

## 概要

本書は、AWS 本番環境（prd）への CI/CD システム構築手順を定義します。本番環境では、開発環境・検証環境とは異なる厳格な権限制御とセキュリティ要件が適用されます。

## 前提条件

- AWS 本番環境アカウント（************）へのアクセス権限
- CloudFormation スタック作成権限
- IAM ロール・ポリシー作成権限
- Secrets Manager 操作権限
- 本番環境 VPC の設定情報

## 1. 本番環境の特性と制約事項

### 1.1 基本方針

- **手動リリースのみ**: 自動デプロイメカニズムは使用しない
- **最小権限の原則**: 必要最小限の権限のみ付与
- **承認プロセス**: デプロイ前の多段階承認が必要
- **監査ログ**: すべての操作を詳細に記録

### 1.2 環境固有設定

| 項目                   | 本番環境設定                   |
| ---------------------- | ------------------------------ |
| 対象ブランチ           | master                         |
| CodeBuild プロジェクト | cdp-prd-dlpf-deploy            |
| Parameter Store        | /dlpf/prd/deploy-baseline-time |
| GitHub App 認証情報    | github-app-credentials         |
| SNS トピック           | なし（エラー通知無効）         |
| エラー通知             | 無効                           |
| 自動デプロイ           | 無効（手動実行のみ）           |

## 2. 構築手順

### Step 1: 環境設定の確認

#### Step 1-1: AWS 環境の切り替え

```bash
# 本番環境用のAWS認証情報に切り替え
rm ~/.aws
ln -s ~/.aws.prd ~/.aws

# 環境確認
aws sts get-caller-identity
# Account ID: ************ であることを確認
```

#### Step 1-2: 本番環境パラメータファイルの確認

```bash
# 本番環境パラメータファイルの存在確認
ls -la cloudformation/environments/prd/parameters/
cat cloudformation/environments/prd/parameters/default.json
```

### Step 2: GitHub App 認証情報の設定

#### Step 2-1: Secrets Manager 設定確認

```bash
# GitHub App認証情報の存在確認
aws secretsmanager describe-secret --secret-id github-app-credentials

# 認証情報の内容確認（本番環境用設定であることを確認）
aws secretsmanager get-secret-value --secret-id github-app-credentials
```

#### Step 2-2: GitHub App 権限の確認

- 本番環境用 GitHub App の権限設定を確認
- リポジトリアクセス権限の最小化
- Webhook 設定の無効化（自動デプロイ防止）

### Step 3: VPC・ネットワーク設定

#### Step 3-1: VPC 情報の取得

```bash
# 本番環境VPC情報の確認
aws ec2 describe-vpcs --filters "Name=tag:Environment,Values=prd"
aws ec2 describe-subnets --filters "Name=tag:Environment,Values=prd"
aws ec2 describe-security-groups --filters "Name=tag:Environment,Values=prd"
```

#### Step 3-2: CodeBuild 用ネットワーク設定の準備

- プライベートサブネットの選択
- セキュリティグループの設定確認
- NAT ゲートウェイ経由のインターネットアクセス確認

### Step 4: IAM 権限の設定

#### Step 4-1: 本番環境用 IAM ロールの権限見直し

本番環境では、開発環境より厳格な権限制御を適用：

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "cloudformation:DescribeStacks",
        "cloudformation:CreateStack",
        "cloudformation:UpdateStack",
        "cloudformation:CreateChangeSet",
        "cloudformation:DescribeChangeSet",
        "cloudformation:ExecuteChangeSet",
        "cloudformation:DeleteChangeSet"
      ],
      "Resource": "arn:aws:cloudformation:ap-northeast-1:************:stack/dlpf-prd-*"
    }
  ]
}
```

#### Step 4-2: 本番環境固有の制約

- CloudFormation スタック削除権限の除外
- 特定リソースタイプのみへの権限制限
- 時間制限付きアクセス（必要に応じて）

### Step 5: CodeBuild プロジェクトの構築

#### Step 5-1: 本番環境パラメータファイルの設定

```bash
# 本番環境用パラメータファイルの編集
# VPC ID、サブネット ID、セキュリティグループ ID、CodeConnections ARNを実際の値に更新
vi cicd/cloudformation/environments/prd/parameters/codebuild/default.json

# パラメータファイルの例:
# {
#   "Parameters": {
#     "VpcId": "vpc-0123456789abcdef0",
#     "SubnetIds": "subnet-0123456789abcdef0,subnet-0fedcba9876543210",
#     "SecurityGroupIds": "sg-0123456789abcdef0",
#     "GitHubAppSecretName": "github-app-credentials",
#     "CodeConnectionsArn": "arn:aws:codeconnections:ap-northeast-1:************:connection/12345678-1234-1234-1234-123456789012"
#   }
# }
```

#### Step 5-2: CloudFormation テンプレートのデプロイ

```bash
# 本番環境用CodeBuildプロジェクトのデプロイ
cd cicd/cloudformation
./scripts/cfn_deploy.sh -e prd -n codebuild cicd-codebuild-deploy

# デプロイ結果の確認
aws codebuild list-projects | grep cdp-prd-dlpf-deploy
```

#### Step 5-3: Parameter Store の初期設定

```bash
# 本番環境用Parameter Storeの設定
aws ssm put-parameter \
  --name "/dlpf/prd/deploy-baseline-time" \
  --value '{"deploy_baseline_time": "1970-01-01T00:00:00Z"}' \
  --type "String" \
  --description "本番環境デプロイ基準時刻"

# system-managerテンプレートのデプロイ
cd cicd/cloudformation
./scripts/cfn_deploy.sh -e prd -n system-manager parameter-store
```

### Step 6: セキュリティ設定の強化

#### Step 6-1: CloudTrail 設定の確認

```bash
# CloudTrailでCodeBuild操作のログ記録を確認
aws cloudtrail describe-trails
aws cloudtrail get-event-selectors --trail-name <trail-name>
```

#### Step 6-2: 監査ログ設定

- CodeBuild プロジェクトの CloudWatch ログ設定確認
- S3 バケットへのログアーカイブ設定
- ログ保持期間の設定（法的要件に応じて）

## 3. 本番環境デプロイ手順

### 3.1 事前準備

#### Step 3.1-1: デプロイ対象の確認

```bash
# masterブランチの最新状態確認
git checkout master
git pull origin master

# デプロイ対象PRの確認
./cicd/scripts/deploy.sh prd --dry-run
```

#### Step 3.1-2: 承認プロセス

1. **技術責任者による承認**

   - デプロイ対象の変更内容レビュー
   - 影響範囲の確認
   - ロールバック計画の確認

2. **運用責任者による承認**
   - デプロイタイミングの承認
   - 業務影響の確認
   - 緊急連絡体制の確認

### 3.2 デプロイ実行

#### Step 3.2-1: 手動デプロイの実行

```bash
# 本番環境への手動デプロイ
./cicd/scripts/deploy.sh prd "2025-01-01T00:00:00Z"

# または特定時刻以降のPRをデプロイ
./cicd/scripts/deploy.sh prd "2025-07-20T10:00:00Z"
```

#### Step 3.2-2: デプロイ監視

```bash
# CodeBuildの実行状況監視
aws codebuild list-builds-for-project --project-name cdp-prd-dlpf-deploy

# CloudWatchログの確認
aws logs describe-log-groups --log-group-name-prefix "/aws/codebuild/cdp-prd-dlpf"
```

### 3.3 デプロイ後検証

#### Step 3.3-1: デプロイ結果の確認

- CloudFormation スタックの状態確認
- Glue ジョブの更新確認
- Lambda 関数の更新確認

#### Step 3.3-2: 動作確認

- 本番環境での基本動作テスト
- データ処理パイプラインの動作確認
- エラーログの確認

## 4. 緊急時対応

### 4.1 ロールバック手順

```bash
# 前回の正常状態への復旧
./cicd/scripts/deploy.sh prd "2025-07-19T00:00:00Z"

# 特定のCloudFormationスタックのロールバック
aws cloudformation cancel-update-stack --stack-name <stack-name>
```

### 4.2 緊急連絡体制

- 技術責任者: [連絡先]
- 運用責任者: [連絡先]
- AWS 技術サポート: [契約に応じて]

## 5. 運用・保守

### 5.1 定期メンテナンス

- 月次での IAM 権限レビュー
- 四半期でのセキュリティ設定見直し
- 年次でのディザスタリカバリテスト

### 5.2 監査対応

- デプロイ履歴の定期レポート作成
- 変更管理台帳の更新
- コンプライアンス要件への対応

## 6. トラブルシューティング

### 6.1 よくある問題と対処法

#### 問題 1: GitHub App 認証エラー

```bash
# 認証情報の再確認
aws secretsmanager get-secret-value --secret-id github-app-credentials
```

#### 問題 2: VPC 接続エラー

```bash
# ネットワーク設定の確認
aws ec2 describe-route-tables --filters "Name=vpc-id,Values=<vpc-id>"
```

#### 問題 3: 権限不足エラー

```bash
# IAMロールの権限確認
aws iam get-role-policy --role-name cdp-prd-dlpf-deploy-role --policy-name CodeBuildDeployPermissions
```

## 7. 本番環境固有の設定詳細

### 7.1 権限制御の詳細設定

#### 7.1-1: 本番環境用 IAM ポリシーの例

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "CloudFormationLimitedAccess",
      "Effect": "Allow",
      "Action": [
        "cloudformation:DescribeStacks",
        "cloudformation:CreateStack",
        "cloudformation:UpdateStack",
        "cloudformation:CreateChangeSet",
        "cloudformation:DescribeChangeSet",
        "cloudformation:ExecuteChangeSet",
        "cloudformation:DeleteChangeSet",
        "cloudformation:ValidateTemplate",
        "cloudformation:GetTemplateSummary"
      ],
      "Resource": [
        "arn:aws:cloudformation:ap-northeast-1:************:stack/dlpf-prd-*",
        "arn:aws:cloudformation:ap-northeast-1:************:stackset/dlpf-prd-*"
      ]
    },
    {
      "Sid": "S3LimitedAccess",
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::aws-glue-assets-************-ap-northeast-1",
        "arn:aws:s3:::aws-glue-assets-************-ap-northeast-1/*",
        "arn:aws:s3:::dlpf-prd-*",
        "arn:aws:s3:::dlpf-prd-*/*"
      ]
    },
    {
      "Sid": "GlueLimitedAccess",
      "Effect": "Allow",
      "Action": ["glue:UpdateJob", "glue:GetJob", "glue:GetJobs"],
      "Resource": ["arn:aws:glue:ap-northeast-1:************:job/dlpf_*"]
    },
    {
      "Sid": "LambdaLimitedAccess",
      "Effect": "Allow",
      "Action": [
        "lambda:UpdateFunctionCode",
        "lambda:GetFunction",
        "lambda:UpdateFunctionConfiguration"
      ],
      "Resource": [
        "arn:aws:lambda:ap-northeast-1:************:function:dlpf-prd-*"
      ]
    }
  ]
}
```

#### 7.1-2: 除外される権限（本番環境では付与しない）

- CloudFormation スタックの削除権限
- IAM ロール・ポリシーの作成・変更権限
- VPC・ネットワーク設定の変更権限
- Secrets Manager の変更権限（読み取りのみ）

### 7.2 セキュリティ強化設定

#### 7.2-1: CodeBuild プロジェクトのセキュリティ設定

```yaml
# 本番環境用CodeBuildプロジェクトの追加設定
Environment:
  Type: LINUX_CONTAINER
  ComputeType: BUILD_GENERAL1_MEDIUM
  Image: aws/codebuild/standard:6.0
  PrivilegedMode: false # 本番環境では無効化
  EnvironmentVariables:
    - Name: ENVIRONMENT
      Type: PLAINTEXT
      Value: prd
    - Name: DRY_RUN
      Type: PLAINTEXT
      Value: "false"
    - Name: DEBUG_MODE
      Type: PLAINTEXT
      Value: "false" # 本番環境では無効
    - Name: SNS_NOTIFICATION_ENABLED
      Type: PLAINTEXT
      Value: "false" # 本番環境では無効
```

#### 7.2-2: ネットワークセキュリティ

- プライベートサブネットでの実行
- インターネットアクセスは NAT ゲートウェイ経由のみ
- セキュリティグループでの厳格なアクセス制御
- VPC エンドポイントの活用（AWS API 呼び出し用）

### 7.3 監査・コンプライアンス設定

#### 7.3-1: CloudTrail 設定

```bash
# 本番環境用CloudTrail設定の確認
aws cloudtrail describe-trails --trail-name-list dlpf-prd-audit-trail

# CodeBuild関連イベントの記録確認
aws cloudtrail lookup-events \
  --lookup-attributes AttributeKey=EventName,AttributeValue=StartBuild \
  --start-time 2025-07-01 \
  --end-time 2025-07-22
```

#### 7.3-2: ログ保持設定

- CloudWatch ログ: 1 年間保持
- S3 アーカイブ: 7 年間保持
- 監査ログ: 法的要件に応じた長期保持

## 8. 本番環境デプロイチェックリスト

### 8.1 デプロイ前チェックリスト

- [ ] master ブランチが最新状態である
- [ ] 検証環境での動作確認が完了している
- [ ] 影響範囲分析が完了している
- [ ] ロールバック計画が準備されている
- [ ] 技術責任者の承認を得ている
- [ ] 運用責任者の承認を得ている
- [ ] デプロイ時間帯が適切である
- [ ] 緊急連絡体制が整っている

### 8.2 デプロイ実行チェックリスト

- [ ] AWS 本番環境に接続している
- [ ] デプロイ対象が正しく特定されている
- [ ] DRY-RUN での事前確認を実施した
- [ ] デプロイ実行ログを監視している
- [ ] エラーが発生していない

### 8.3 デプロイ後チェックリスト

- [ ] CloudFormation スタックが正常状態である
- [ ] Glue ジョブが正常に更新されている
- [ ] Lambda 関数が正常に更新されている
- [ ] 基本動作テストが完了している
- [ ] エラーログに異常がない
- [ ] Parameter Store が正しく更新されている
- [ ] デプロイ完了報告を実施した

## 9. 本番環境特有の注意事項

### 9.1 デプロイタイミング

- 業務時間外での実行を原則とする
- システムメンテナンス時間帯の活用
- 大型連休前の実行は避ける
- 月末・四半期末の実行は慎重に検討

### 9.2 変更管理

- すべてのデプロイは変更管理台帳に記録
- 事前の影響範囲分析を必須とする
- 承認プロセスの文書化と遵守
- デプロイ後の結果報告を必須とする

### 9.3 緊急時対応

- 24 時間以内のロールバック体制
- エスカレーション手順の明確化
- 関係者への迅速な連絡体制
- 事後分析と改善策の検討

## 10. 実際の構築例

### 10.1 本番環境構築の完全な手順例

```bash
# 1. AWS環境の切り替え
rm ~/.aws
ln -s ~/.aws.prd ~/.aws
aws sts get-caller-identity  # Account ID: ************ を確認

# 2. 本番環境VPC情報の取得
aws ec2 describe-vpcs --filters "Name=tag:Environment,Values=prd" --query 'Vpcs[0].VpcId' --output text
aws ec2 describe-subnets --filters "Name=tag:Environment,Values=prd" --query 'Subnets[?MapPublicIpOnLaunch==`false`].[SubnetId]' --output text
aws ec2 describe-security-groups --filters "Name=tag:Environment,Values=prd" --query 'SecurityGroups[0].GroupId' --output text

# 3. CodeConnections ARNの取得
aws codeconnections list-connections --provider-type GitHub --query 'Connections[0].ConnectionArn' --output text

# 4. パラメータファイルの更新
vi cicd/cloudformation/environments/prd/parameters/codebuild/default.json
# 上記で取得した値を設定

# 5. GitHub App認証情報の確認
aws secretsmanager get-secret-value --secret-id github-app-credentials

# 6. CodeBuildプロジェクトのデプロイ
cd cicd/cloudformation
./scripts/cfn_deploy.sh -e prd -n codebuild cicd-codebuild-deploy

# 7. Parameter Storeの設定
./scripts/cfn_deploy.sh -e prd -n system-manager parameter-store

# 8. 構築結果の確認
aws codebuild list-projects | grep cdp-prd-dlpf-deploy
aws ssm get-parameter --name "/dlpf/prd/deploy-baseline-time"
```

### 10.2 初回デプロイテスト

```bash
# DRY-RUNでの動作確認
./cicd/scripts/deploy.sh prd --dry-run

# 実際のデプロイ実行（初回は全PRが対象）
./cicd/scripts/deploy.sh prd

# デプロイ結果の確認
aws codebuild list-builds-for-project --project-name cdp-prd-dlpf-deploy --sort-order DESCENDING --max-items 1
```

## 11. 関連ドキュメント

- [AWS インフラリソース一覧](aws-resources.md)
- [デプロイシステム設計書](deployment-system.md)
- [運用ガイド](operation-guide.md)
- [CloudFormation 手動デプロイ手順書](../../cloudformation/docs/CloudFormation手動デプロイ手順書.md)

## 変更履歴

| 日付       | バージョン | 変更者 | 変更内容 |
| ---------- | ---------- | ------ | -------- |
| 2025-07-22 | 1.0        | TIS 黄 | 初版作成 |

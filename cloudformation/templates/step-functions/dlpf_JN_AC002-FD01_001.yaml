AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_AC002-FD01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for AC002-FD01",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "DLPF_CHECK_FILE_EXIST"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "DLPF_CHECK_FILE_EXIST": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Next": "CheckFileExists",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:ap-northeast-1:${AccountId}:function:DLPF_CHECK_FILE_EXIST:$LATEST",
                "Payload": {
                  "dir_path": "input-output/SQG_IN/JN_AC002-FD01_001_*/",
                  "file_name": "Netshopping.GL010.csv",
                  "backup_dir": "back-up/DLPF_CHECK_FILE_EXIST/"
                }
              },
              "ResultPath": "$.lambdaResult"
            },
            "CheckFileExists": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.lambdaResult.Payload.is_execute",
                  "BooleanEquals": true,
                  "Next": "GetCurrentDate"
                }
              ],
              "Default": "end_file_not_found"
            },
            "end_file_not_found": {
              "Type": "Pass",
              "End": true
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "1_job_internal_db_import"
            },
            "1_job_internal_db_import": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_import",
                "Arguments": {
                  "input_format_options": "(FORMAT CSV, HEADER false, ENCODING 'SJIS')",
                  "secret_name": "DLPF_DB_INFO",
                  "input_file_dir": "input-output/SQG_IN/JN_AC002-FD01_001_/",
                  "input_file_name": "Netshopping.GL010.csv",
                  "import_table": "accounting_data_store_sales_work",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/SQG_IN/JN_AC002-FD01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_AC002-FD01_001",
                  "query_upsert": "sql_AC002-FD01_upsert_001"
                }
              },
              "End": true
            }
          }
        }
